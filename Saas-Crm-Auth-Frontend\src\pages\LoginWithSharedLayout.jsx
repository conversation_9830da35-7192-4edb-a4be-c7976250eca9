import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Mail, Lock, LogIn } from 'lucide-react';

import { AuthLayout } from '@saas-crm/layouts';
import {
    Button,
    Input,
    Label,
    Alert,
    AlertDescription,
    Checkbox
} from '@saas-crm/components';

import { useAuth } from '../contexts/AuthContext';

const loginSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .refine((email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email), {
            message: 'Please enter a valid email address',
        }),
    password: z
        .string()
        .min(1, 'Password is required')
        .min(6, 'Password must be at least 6 characters'),
    rememberMe: z.boolean().optional(),
});

const LoginWithSharedLayout = () => {
    const [showPassword, setShowPassword] = useState(false);
    const { login, isLoading, error, isAuthenticated, clearError } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        if (isAuthenticated) {
            const from = location.state?.from?.pathname || '/dashboard';
            navigate(from, { replace: true });
        }
    }, [isAuthenticated, navigate, location]);

    useEffect(() => {
        clearError();
    }, [clearError]);

    const {
        register,
        handleSubmit,
        control,
        formState: { errors, isSubmitting },
        setError,
    } = useForm({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
            rememberMe: false,
        },
    });

    const onSubmit = async (data) => {
        try {
            const result = await login({
                email: data.email,
                password: data.password,
                remember_me: data.rememberMe,
            });

            if (!result.success) {
                if (result.error?.includes('email')) {
                    setError('email', { message: result.error });
                } else if (result.error?.includes('password')) {
                    setError('password', { message: result.error });
                }
            }
        } catch (error) {
            console.error('Login error:', error);
        }
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    return (
        <AuthLayout
            title="Welcome Back"
            description="Sign in to your Accord account"
            className="bg-gradient-to-br from-blue-50 via-white to-purple-50"
        >
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {error && (
                    <Alert variant="destructive" className="border-red-200 bg-red-50">
                        <AlertDescription className="text-red-800">{error}</AlertDescription>
                    </Alert>
                )}

                <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                        Email Address
                    </Label>
                    <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                            id="email"
                            type="email"
                            placeholder="Enter your email"
                            className="pl-10 h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                            {...register('email')}
                            disabled={isLoading || isSubmitting}
                        />
                    </div>
                    {errors.email && (
                        <p className="text-sm text-red-600">{errors.email.message}</p>
                    )}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                        Password
                    </Label>
                    <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                            id="password"
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter your password"
                            className="pl-10 pr-10 h-11 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                            {...register('password')}
                            disabled={isLoading || isSubmitting}
                        />
                        <button
                            type="button"
                            className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600 transition-colors"
                            onClick={togglePasswordVisibility}
                            disabled={isLoading || isSubmitting}
                        >
                            {showPassword ? <EyeOff /> : <Eye />}
                        </button>
                    </div>
                    {errors.password && (
                        <p className="text-sm text-red-600">{errors.password.message}</p>
                    )}
                </div>

                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                        <Controller
                            name="rememberMe"
                            control={control}
                            render={({ field }) => (
                                <Checkbox
                                    id="rememberMe"
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    disabled={isLoading || isSubmitting}
                                />
                            )}
                        />
                        <Label
                            htmlFor="rememberMe"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700"
                        >
                            Remember me
                        </Label>
                    </div>
                    <Link
                        to="/forgot-password"
                        className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
                    >
                        Forgot password?
                    </Link>
                </div>

                <Button
                    type="submit"
                    className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                    disabled={isLoading || isSubmitting}
                >
                    {isLoading || isSubmitting ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Signing In...
                        </>
                    ) : (
                        <>
                            <LogIn className="mr-2 h-4 w-4" />
                            Sign In
                        </>
                    )}
                </Button>

                {import.meta.env.VITE_ENABLE_REGISTRATION === 'true' && (
                    <div className="text-center text-sm">
                        <span className="text-gray-600">Don't have an account? </span>
                        <Link
                            to="/register"
                            className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
                        >
                            Sign up here
                        </Link>
                    </div>
                )}

                <div className="text-center text-xs text-gray-500">
                    <p>
                        By signing in, you agree to our{' '}
                        <Link to="/terms" className="text-blue-600 hover:text-blue-800 transition-colors">
                            Terms of Service
                        </Link>{' '}
                        and{' '}
                        <Link to="/privacy" className="text-blue-600 hover:text-blue-800 transition-colors">
                            Privacy Policy
                        </Link>
                    </p>
                </div>
            </form>
        </AuthLayout>
    );
};

export default LoginWithSharedLayout;
