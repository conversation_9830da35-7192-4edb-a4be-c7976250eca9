import React from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@saas-crm/layouts';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
    Button
} from '@saas-crm/components';
import {
    Home,
    Users,
    Settings,
    BarChart3,
    CreditCard,
    UserPlus,
    TrendingUp,
    Activity
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const DashboardExample = () => {
    const navigate = useNavigate();
    const { user, logout } = useAuth();

    // Custom navigation items for this app
    const navigationItems = [
        { href: '/dashboard', label: 'Dashboard', icon: Home },
        { href: '/users', label: 'Users', icon: Users },
        { href: '/analytics', label: 'Analytics', icon: BarChart3 },
        { href: '/billing', label: 'Billing', icon: CreditCard },
        { href: '/settings', label: 'Settings', icon: Settings },
    ];

    const handleLogout = async () => {
        await logout();
        navigate('/login');
    };

    // Mock data for dashboard cards
    const stats = [
        {
            title: 'Total Users',
            value: '2,543',
            change: '+12%',
            icon: Users,
            color: 'text-blue-600',
            bgColor: 'bg-blue-100'
        },
        {
            title: 'Active Sessions',
            value: '1,234',
            change: '+8%',
            icon: Activity,
            color: 'text-green-600',
            bgColor: 'bg-green-100'
        },
        {
            title: 'Revenue',
            value: '$45,231',
            change: '+23%',
            icon: TrendingUp,
            color: 'text-purple-600',
            bgColor: 'bg-purple-100'
        },
        {
            title: 'New Signups',
            value: '156',
            change: '+5%',
            icon: UserPlus,
            color: 'text-orange-600',
            bgColor: 'bg-orange-100'
        }
    ];

    return (
        <DashboardLayout
            navigationItems={navigationItems}
            user={user}
            onLogout={handleLogout}
            title="Accord CRM"
        >
            <div className="space-y-6">
                {/* Page Header */}
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
                    <p className="text-gray-600 mt-2">
                        Welcome back, {user?.name || 'User'}! Here's what's happening with your account.
                    </p>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {stats.map((stat, index) => {
                        const Icon = stat.icon;
                        return (
                            <Card key={index} className="hover:shadow-lg transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                                            <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                                            <p className="text-sm text-green-600 mt-1">{stat.change} from last month</p>
                                        </div>
                                        <div className={`p-3 rounded-full ${stat.bgColor}`}>
                                            <Icon className={`h-6 w-6 ${stat.color}`} />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Recent Activity */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Activity</CardTitle>
                            <CardDescription>Latest actions in your account</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {[
                                    { action: 'New user registered', time: '2 minutes ago', user: '<EMAIL>' },
                                    { action: 'Payment received', time: '1 hour ago', user: '<EMAIL>' },
                                    { action: 'Profile updated', time: '3 hours ago', user: '<EMAIL>' },
                                    { action: 'New subscription', time: '5 hours ago', user: '<EMAIL>' },
                                ].map((activity, index) => (
                                    <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                                            <p className="text-xs text-gray-500">{activity.user}</p>
                                        </div>
                                        <p className="text-xs text-gray-400">{activity.time}</p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common tasks and shortcuts</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                                <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                                    <UserPlus className="h-6 w-6" />
                                    <span className="text-sm">Add User</span>
                                </Button>
                                <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                                    <BarChart3 className="h-6 w-6" />
                                    <span className="text-sm">View Reports</span>
                                </Button>
                                <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                                    <CreditCard className="h-6 w-6" />
                                    <span className="text-sm">Billing</span>
                                </Button>
                                <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                                    <Settings className="h-6 w-6" />
                                    <span className="text-sm">Settings</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Chart Placeholder */}
                <Card>
                    <CardHeader>
                        <CardTitle>Analytics Overview</CardTitle>
                        <CardDescription>User growth and engagement metrics</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div className="text-center">
                                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">Chart component would go here</p>
                                <p className="text-sm text-gray-400">Integration with charting library needed</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </DashboardLayout>
    );
};

export default DashboardExample;
