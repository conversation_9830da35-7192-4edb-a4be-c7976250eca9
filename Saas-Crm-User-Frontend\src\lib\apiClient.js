import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance with default configuration
const apiClient = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Simple token management using sessionStorage
const getToken = () => sessionStorage.getItem('token');
const clearSession = () => sessionStorage.clear();

// Request interceptor to add auth token
apiClient.interceptors.request.use(
    (config) => {
        const token = getToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        if (import.meta.env.VITE_DEBUG === 'true') {
            console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
                headers: config.headers,
                data: config.data,
                params: config.params,
            });
        }

        return config;
    },
    (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
    }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
    (response) => {
        if (import.meta.env.VITE_DEBUG === 'true') {
            console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, {
                status: response.status,
                data: response.data,
            });
        }

        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        if (import.meta.env.VITE_DEBUG === 'true') {
            console.error('[API Response Error]', {
                url: error.config?.url,
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
        }

        // Handle 401 Unauthorized - clear session and let auth context handle
        if (error.response?.status === 401) {
            clearSession();
            toast.error('Session expired. Please login again.');
        }

        const errorMessage = error.response?.data?.message ||
            error.response?.data?.error ||
            error.message ||
            'An unexpected error occurred';

        if (!originalRequest.skipErrorToast) {
            switch (error.response?.status) {
                case 400:
                    toast.error(errorMessage);
                    break;
                case 403:
                    toast.error('Access denied. You don\'t have permission to perform this action.');
                    break;
                case 404:
                    toast.error('Resource not found.');
                    break;
                case 422:
                    break;
                case 500:
                    toast.error('Server error. Please try again later.');
                    break;
                default:
                    if (error.response?.status >= 400) {
                        toast.error(errorMessage);
                    }
            }
        }

        return Promise.reject(error);
    }
);

export const authAPI = {
    login: (credentials) => apiClient.post('/auth/login', credentials),
    register: (userData) => apiClient.post('/auth/register', userData),
    logout: () => apiClient.post('/auth/logout'),
    forgotPassword: (email) => apiClient.post('/auth/forgot-password', { email }),
    resetPassword: (token, password, passwordConfirmation) =>
        apiClient.post('/auth/reset-password', {
            token,
            password,
            password_confirmation: passwordConfirmation
        }),
    getProfile: () => apiClient.get('/auth/profile'),
    updateProfile: (userData) => apiClient.put('/auth/profile', userData),
    changePassword: (currentPassword, newPassword, passwordConfirmation) =>
        apiClient.put('/auth/change-password', {
            current_password: currentPassword,
            new_password: newPassword,
            password_confirmation: passwordConfirmation,
        }),
};

export const api = {
    get: (url, config = {}) => apiClient.get(url, config),
    post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
    put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
    patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
    delete: (url, config = {}) => apiClient.delete(url, config),
};

export default apiClient;
