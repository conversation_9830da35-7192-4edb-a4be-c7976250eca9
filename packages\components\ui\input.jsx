import * as React from "react"

import { cn } from "../lib/utils"

function Input({
    className,
    type,
    ...props
}) {
    return (
        <input
            type={type}
            data-slot="input"
            className={cn(
                "flex h-[var(--height-input)] w-full min-w-0 rounded-[var(--radius-md)] border border-[var(--color-border)] bg-[var(--color-background)] px-[var(--input-padding-x)] py-[var(--input-padding-y)] text-[var(--font-sm)] shadow-[var(--shadow-xs)] transition-[var(--transition-normal)] outline-none",
                "placeholder:text-[var(--color-muted-foreground)] selection:bg-[var(--color-primary)] selection:text-[var(--color-primary-foreground)]",
                "focus-visible:outline-2 focus-visible:outline-offset-0 focus-visible:outline-[var(--color-ring)] focus-visible:border-[var(--color-ring)]",
                "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
                "file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-[var(--font-sm)] file:font-[var(--font-weight-medium)] file:text-[var(--color-foreground)]",
                "aria-invalid:border-[var(--color-destructive)] aria-invalid:outline-[var(--color-destructive)]",
                className
            )}
            {...props} />
    );
}

export { Input }
