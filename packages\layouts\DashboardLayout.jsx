import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
    Button,
    Sheet,
    SheetContent,
    SheetTrigger,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Avatar,
    AvatarFallback,
    AvatarImage
} from '@saas-crm/components';
import {
    Menu,
    Home,
    Users,
    Settings,
    LogOut,
    User,
    Bell,
    Search
} from 'lucide-react';

/**
 * DashboardLayout - A layout with sidebar navigation for dashboard pages
 * @param {Object} props
 * @param {Array} props.navigationItems - Array of navigation items with { href, label, icon }
 * @param {Object} props.user - User object with name, email, avatar
 * @param {Function} props.onLogout - Function to handle logout
 * @param {React.ReactNode} props.children - The content to render (optional, uses Outlet if not provided)
 * @param {string} props.title - The title of the application
 */
export const DashboardLayout = ({
    navigationItems = [],
    user = { name: 'User', email: '<EMAIL>' },
    onLogout,
    children,
    title = "SaaS CRM"
}) => {
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const location = useLocation();

    const defaultNavigationItems = [
        { href: '/dashboard', label: 'Dashboard', icon: Home },
        { href: '/users', label: 'Users', icon: Users },
        { href: '/settings', label: 'Settings', icon: Settings },
    ];

    const navItems = navigationItems.length > 0 ? navigationItems : defaultNavigationItems;

    const Sidebar = ({ className = "" }) => (
        <div className={`flex flex-col h-full bg-white border-r ${className}`}>
            <div className="flex items-center justify-center h-16 px-4 border-b">
                <h1 className="text-xl font-bold text-gray-900">{title}</h1>
            </div>
            <nav className="flex-1 px-4 py-4 space-y-2">
                {navItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = location.pathname === item.href;
                    return (
                        <Link
                            key={item.href}
                            to={item.href}
                            className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${isActive
                                ? 'bg-gray-100 text-gray-900'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                }`}
                            onClick={() => setSidebarOpen(false)}
                        >
                            <Icon className="w-5 h-5 mr-3" />
                            {item.label}
                        </Link>
                    );
                })}
            </nav>
        </div>
    );

    return (
        <div className="flex h-screen bg-gray-100">
            {/* Desktop Sidebar */}
            <div className="hidden md:flex md:w-64 md:flex-col">
                <Sidebar />
            </div>

            {/* Mobile Sidebar */}
            <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
                <SheetContent side="left" className="p-0 w-64">
                    <Sidebar />
                </SheetContent>
            </Sheet>

            {/* Main Content */}
            <div className="flex flex-col flex-1 overflow-hidden">
                {/* Header */}
                <header className="flex items-center justify-between px-4 py-4 bg-white border-b md:px-6">
                    <div className="flex items-center">
                        <Sheet>
                            <SheetTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="md:hidden"
                                    onClick={() => setSidebarOpen(true)}
                                >
                                    <Menu className="w-5 h-5" />
                                </Button>
                            </SheetTrigger>
                        </Sheet>
                    </div>

                    <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm">
                            <Search className="w-5 h-5" />
                        </Button>
                        <Button variant="ghost" size="sm">
                            <Bell className="w-5 h-5" />
                        </Button>

                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                                    <Avatar className="h-8 w-8">
                                        <AvatarImage src={user.avatar} alt={user.name} />
                                        <AvatarFallback>
                                            {user.name?.charAt(0)?.toUpperCase() || 'U'}
                                        </AvatarFallback>
                                    </Avatar>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-56" align="end" forceMount>
                                <DropdownMenuLabel className="font-normal">
                                    <div className="flex flex-col space-y-1">
                                        <p className="text-sm font-medium leading-none">{user.name}</p>
                                        <p className="text-xs leading-none text-muted-foreground">
                                            {user.email}
                                        </p>
                                    </div>
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                    <User className="mr-2 h-4 w-4" />
                                    <span>Profile</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                    <Settings className="mr-2 h-4 w-4" />
                                    <span>Settings</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={onLogout}>
                                    <LogOut className="mr-2 h-4 w-4" />
                                    <span>Log out</span>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </header>

                {/* Page Content */}
                <main className="flex-1 overflow-auto p-4 md:p-6">
                    {children || <Outlet />}
                </main>
            </div>
        </div>
    );
};

export default DashboardLayout;
