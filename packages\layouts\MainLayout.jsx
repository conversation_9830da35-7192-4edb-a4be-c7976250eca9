import React from 'react';
import { Outlet, Link } from 'react-router-dom';
import { Button } from '@saas-crm/components';

/**
 * MainLayout - A simple layout with header and footer for general pages
 * @param {Object} props
 * @param {React.ReactNode} props.children - The content to render (optional, uses Outlet if not provided)
 * @param {string} props.title - The title of the application
 * @param {Array} props.headerLinks - Array of header navigation links with { href, label }
 * @param {boolean} props.showFooter - Whether to show the footer
 * @param {string} props.className - Additional CSS classes for the main container
 */
export const MainLayout = ({
    children,
    title = "SaaS CRM",
    headerLinks = [],
    showFooter = true,
    className = ""
}) => {
    const defaultHeaderLinks = [
        { href: '/', label: 'Home' },
        { href: '/about', label: 'About' },
        { href: '/contact', label: 'Contact' },
    ];

    const navLinks = headerLinks.length > 0 ? headerLinks : defaultHeaderLinks;

    return (
        <div className="min-h-screen flex flex-col bg-white">
            {/* Header */}
            <header className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        {/* Logo/Title */}
                        <div className="flex items-center">
                            <Link to="/" className="text-xl font-bold text-gray-900 hover:text-gray-700">
                                {title}
                            </Link>
                        </div>

                        {/* Navigation */}
                        <nav className="hidden md:flex space-x-8">
                            {navLinks.map((link) => (
                                <Link
                                    key={link.href}
                                    to={link.href}
                                    className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors"
                                >
                                    {link.label}
                                </Link>
                            ))}
                        </nav>

                        {/* Auth Buttons */}
                        <div className="flex items-center space-x-4">
                            <Link to="/login">
                                <Button variant="ghost" size="sm">
                                    Sign In
                                </Button>
                            </Link>
                            <Link to="/register">
                                <Button size="sm">
                                    Sign Up
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className={`flex-1 ${className}`}>
                {children || <Outlet />}
            </main>

            {/* Footer */}
            {showFooter && (
                <footer className="bg-gray-50 border-t">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <div className="col-span-1 md:col-span-2">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
                                <p className="text-gray-600 text-sm">
                                    A comprehensive CRM solution for modern businesses.
                                </p>
                            </div>

                            <div>
                                <h4 className="text-sm font-semibold text-gray-900 mb-4">Product</h4>
                                <ul className="space-y-2">
                                    <li><Link to="/features" className="text-sm text-gray-600 hover:text-gray-900">Features</Link></li>
                                    <li><Link to="/pricing" className="text-sm text-gray-600 hover:text-gray-900">Pricing</Link></li>
                                    <li><Link to="/integrations" className="text-sm text-gray-600 hover:text-gray-900">Integrations</Link></li>
                                </ul>
                            </div>

                            <div>
                                <h4 className="text-sm font-semibold text-gray-900 mb-4">Support</h4>
                                <ul className="space-y-2">
                                    <li><Link to="/help" className="text-sm text-gray-600 hover:text-gray-900">Help Center</Link></li>
                                    <li><Link to="/contact" className="text-sm text-gray-600 hover:text-gray-900">Contact Us</Link></li>
                                    <li><Link to="/privacy" className="text-sm text-gray-600 hover:text-gray-900">Privacy Policy</Link></li>
                                </ul>
                            </div>
                        </div>

                        <div className="mt-8 pt-8 border-t border-gray-200">
                            <p className="text-center text-sm text-gray-600">
                                © {new Date().getFullYear()} {title}. All rights reserved.
                            </p>
                        </div>
                    </div>
                </footer>
            )}
        </div>
    );
};

export default MainLayout;
