import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva } from "class-variance-authority";

import { cn } from "../lib/utils"

const buttonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--color-ring)]",
    {
        variants: {
            variant: {
                default:
                    "bg-[var(--color-primary)] text-[var(--color-primary-foreground)] shadow-[var(--shadow-xs)] hover:bg-[var(--color-primary)]/90 border border-transparent",
                destructive:
                    "bg-[var(--color-destructive)] text-[var(--color-destructive-foreground)] shadow-[var(--shadow-xs)] hover:bg-[var(--color-destructive)]/90 border border-transparent",
                outline:
                    "border border-[var(--color-border)] bg-[var(--color-background)] shadow-[var(--shadow-xs)] hover:bg-[var(--color-accent)] hover:text-[var(--color-accent-foreground)]",
                secondary:
                    "bg-[var(--color-secondary)] text-[var(--color-secondary-foreground)] shadow-[var(--shadow-xs)] hover:bg-[var(--color-secondary)]/80 border border-transparent",
                ghost:
                    "hover:bg-[var(--color-accent)] hover:text-[var(--color-accent-foreground)] border border-transparent",
                link: "text-[var(--color-primary)] underline-offset-4 hover:underline border border-transparent",
            },
            size: {
                sm: "h-[var(--height-button-sm)] px-[var(--button-padding-x-sm)] py-[var(--button-padding-y)] rounded-[var(--radius-sm)] text-[var(--font-sm)] has-[>svg]:px-[var(--spacing-sm)]",
                default: "h-[var(--height-button-md)] px-[var(--button-padding-x-md)] py-[var(--button-padding-y)] rounded-[var(--radius-md)] text-[var(--font-sm)] has-[>svg]:px-[var(--spacing-md)]",
                lg: "h-[var(--height-button-lg)] px-[var(--button-padding-x-lg)] py-[var(--button-padding-y)] rounded-[var(--radius-md)] text-[var(--font-md)] has-[>svg]:px-[var(--spacing-lg)]",
                xl: "h-[var(--height-button-xl)] px-[var(--spacing-2xl)] py-[var(--button-padding-y)] rounded-[var(--radius-lg)] text-[var(--font-lg)] has-[>svg]:px-[var(--spacing-xl)]",
                icon: "size-[var(--height-button-md)] rounded-[var(--radius-md)]",
            },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    }
)

function Button({
    className,
    variant,
    size,
    asChild = false,
    ...props
}) {
    const Comp = asChild ? Slot : "button"

    return (
        <Comp
            data-slot="button"
            className={cn(buttonVariants({ variant, size, className }))}
            {...props} />
    );
}

export { Button, buttonVariants }
